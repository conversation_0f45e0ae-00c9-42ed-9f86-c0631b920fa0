package com.sportal365.articlescheduler.sportsdata.twitter.teamnews.processor;

/**
 * Strategy interface for parsing different types of content from team news responses.
 * Follows the Strategy pattern to allow different parsing implementations.
 */
public interface ContentParsingStrategy {
    
    /**
     * Extracts content from a specific section by index.
     * 
     * @param content The raw content to parse
     * @param sectionIndex The index of the section to extract (0-based)
     * @return The extracted section content, or null if not found
     */
    String extractSectionContent(String content, int sectionIndex);
    
    /**
     * Parses team sections from the content.
     * 
     * @param content The content to parse
     * @return Array of team sections
     */
    String[] parseTeamSections(String content);
    
    /**
     * Checks if this strategy can handle the given content format.
     * 
     * @param content The content to check
     * @return true if this strategy can parse the content, false otherwise
     */
    boolean canHandle(String content);
}
