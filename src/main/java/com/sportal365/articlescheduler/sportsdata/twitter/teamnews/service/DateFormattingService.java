package com.sportal365.articlescheduler.sportsdata.twitter.teamnews.service;

import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Service;

import java.time.LocalDate;
import java.time.format.DateTimeFormatter;

/**
 * Service responsible for formatting dates in team news context.
 * Provides consistent date formatting across the team news module.
 */
@Slf4j
@Service
public class DateFormattingService {
    
    private static final DateTimeFormatter DATE_FORMATTER = DateTimeFormatter.ofPattern("dd.MM.yyyy");
    
    /**
     * Formats match date and time into a readable string.
     * 
     * @param date The match date string
     * @param time The match time string (optional)
     * @return Formatted date string or "Unknown Date" if parsing fails
     */
    public String formatMatchDate(String date, String time) {
        try {
            if (date != null && !date.isEmpty()) {
                LocalDate localDate = LocalDate.parse(date);
                String formattedDate = localDate.format(DATE_FORMATTER);
                
                // Add time if available
                if (time != null && !time.trim().isEmpty()) {
                    return formattedDate + " " + time.trim();
                }
                
                return formattedDate;
            }
        } catch (Exception e) {
            log.warn("Failed to parse date '{}': {}", date, e.getMessage());
        }
        return "Unknown Date";
    }
    
    /**
     * Formats a date string using the default formatter.
     * 
     * @param date The date string to format
     * @return Formatted date string or "Unknown Date" if parsing fails
     */
    public String formatDate(String date) {
        return formatMatchDate(date, null);
    }
    
    /**
     * Gets the date formatter used by this service.
     * 
     * @return The DateTimeFormatter instance
     */
    public DateTimeFormatter getDateFormatter() {
        return DATE_FORMATTER;
    }
}
