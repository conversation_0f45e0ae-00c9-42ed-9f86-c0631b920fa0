package com.sportal365.articlescheduler.sportsdata.twitter.teamnews.client.configuration;

import org.springframework.beans.factory.annotation.Value;
import org.springframework.context.annotation.Bean;
import org.springframework.context.annotation.Configuration;
import org.springframework.web.reactive.function.client.WebClient;

@Configuration
public class XaiApiClientConfiguration {

    @Value("${xai.api.base.url}")
    private String xaiApiBaseUrl;

    @Value("${xai.api.key}")
    private String xaiApiKey;

    @Bean
    public WebClient xaiApiWebClient() {
        return WebClient.builder()
                .baseUrl(xaiApiBaseUrl)
                .defaultHeaders(headers -> {
                    headers.set("Content-Type", "application/json");
                    headers.set("Authorization", "Bearer " + xaiApiKey);
                })
                .build();
    }
}
