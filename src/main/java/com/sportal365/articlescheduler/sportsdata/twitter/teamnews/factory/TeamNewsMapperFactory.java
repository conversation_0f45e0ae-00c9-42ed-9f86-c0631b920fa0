package com.sportal365.articlescheduler.sportsdata.twitter.teamnews.factory;

import com.sportal365.articlescheduler.sportsdata.twitter.teamnews.builder.PromptTemplateBuilder;
import com.sportal365.articlescheduler.sportsdata.twitter.teamnews.builder.TeamNewsPromptTemplate;
import com.sportal365.articlescheduler.sportsdata.twitter.teamnews.builder.TeamNewsRequestBuilder;
import com.sportal365.articlescheduler.sportsdata.twitter.teamnews.builder.XaiRequestBuilder;
import com.sportal365.articlescheduler.sportsdata.twitter.teamnews.mapper.ResponseToDomainMapper;
import com.sportal365.articlescheduler.sportsdata.twitter.teamnews.mapper.TeamNewsDataMapper;
import com.sportal365.articlescheduler.sportsdata.twitter.teamnews.processor.ContentCleaningStrategy;
import com.sportal365.articlescheduler.sportsdata.twitter.teamnews.processor.ContentParsingStrategy;
import com.sportal365.articlescheduler.sportsdata.twitter.teamnews.service.DateFormattingService;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Component;

import java.util.Arrays;
import java.util.Set;

/**
 * Implementation of MapperFactory for team news components.
 * Creates different types of mappers and builders based on the requested type.
 */
@Slf4j
@Component
@RequiredArgsConstructor
public class TeamNewsMapperFactory implements MapperFactory {
    
    private final ContentParsingStrategy contentParsingStrategy;
    private final ContentCleaningStrategy contentCleaningStrategy;
    private final DateFormattingService dateFormattingService;
    
    // Supported types
    private static final Set<String> SUPPORTED_BUILDER_TYPES = Set.of("team_news", "default");
    private static final Set<String> SUPPORTED_MAPPER_TYPES = Set.of("team_news", "default");
    private static final Set<String> SUPPORTED_TEMPLATE_TYPES = Set.of("team_news", "default");
    
    @Override
    public XaiRequestBuilder createRequestBuilder(String builderType) {
        if (!supportsBuilderType(builderType)) {
            throw new IllegalArgumentException("Unsupported builder type: " + builderType);
        }
        
        log.debug("Creating request builder for type: {}", builderType);
        
        switch (builderType.toLowerCase()) {
            case "team_news":
            case "default":
                return new TeamNewsRequestBuilder();
            default:
                throw new IllegalArgumentException("Unknown builder type: " + builderType);
        }
    }
    
    @Override
    public ResponseToDomainMapper createResponseMapper(String mapperType) {
        if (!supportsMapperType(mapperType)) {
            throw new IllegalArgumentException("Unsupported mapper type: " + mapperType);
        }
        
        log.debug("Creating response mapper for type: {}", mapperType);
        
        switch (mapperType.toLowerCase()) {
            case "team_news":
            case "default":
                return new TeamNewsDataMapper(contentParsingStrategy, contentCleaningStrategy);
            default:
                throw new IllegalArgumentException("Unknown mapper type: " + mapperType);
        }
    }
    
    @Override
    public PromptTemplateBuilder createPromptTemplateBuilder(String templateType) {
        if (!supportsTemplateType(templateType)) {
            throw new IllegalArgumentException("Unsupported template type: " + templateType);
        }
        
        log.debug("Creating prompt template builder for type: {}", templateType);
        
        switch (templateType.toLowerCase()) {
            case "team_news":
            case "default":
                return new TeamNewsPromptTemplate(dateFormattingService);
            default:
                throw new IllegalArgumentException("Unknown template type: " + templateType);
        }
    }
    
    @Override
    public String[] getSupportedBuilderTypes() {
        return SUPPORTED_BUILDER_TYPES.toArray(new String[0]);
    }
    
    @Override
    public String[] getSupportedMapperTypes() {
        return SUPPORTED_MAPPER_TYPES.toArray(new String[0]);
    }
    
    @Override
    public String[] getSupportedTemplateTypes() {
        return SUPPORTED_TEMPLATE_TYPES.toArray(new String[0]);
    }
    
    @Override
    public boolean supportsBuilderType(String builderType) {
        return builderType != null && SUPPORTED_BUILDER_TYPES.contains(builderType.toLowerCase());
    }
    
    @Override
    public boolean supportsMapperType(String mapperType) {
        return mapperType != null && SUPPORTED_MAPPER_TYPES.contains(mapperType.toLowerCase());
    }
    
    @Override
    public boolean supportsTemplateType(String templateType) {
        return templateType != null && SUPPORTED_TEMPLATE_TYPES.contains(templateType.toLowerCase());
    }
    
    /**
     * Creates a default set of components for team news processing.
     * 
     * @return A configured set of components
     */
    public TeamNewsComponents createDefaultComponents() {
        log.debug("Creating default team news components");
        
        return TeamNewsComponents.builder()
                .requestBuilder(createRequestBuilder("default"))
                .responseMapper(createResponseMapper("default"))
                .promptTemplateBuilder(createPromptTemplateBuilder("default"))
                .build();
    }
    
    /**
     * Data class to hold a complete set of team news components.
     */
    @lombok.Builder
    @lombok.Data
    public static class TeamNewsComponents {
        private XaiRequestBuilder requestBuilder;
        private ResponseToDomainMapper responseMapper;
        private PromptTemplateBuilder promptTemplateBuilder;
    }
}
