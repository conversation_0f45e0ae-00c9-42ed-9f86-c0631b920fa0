package com.sportal365.articlescheduler.sportsdata.twitter.teamnews.client;

import com.sportal365.articlescheduler.infrastructure.client.common.BaseApiClient;
import com.sportal365.articlescheduler.sportsdata.twitter.teamnews.model.XaiApiRequest;
import com.sportal365.articlescheduler.sportsdata.twitter.teamnews.model.XaiApiResponse;
import com.sportal365.articlescheduler.sportsdata.twitter.teamnews.constants.TeamNewsConstants;
import lombok.extern.slf4j.Slf4j;
import org.springframework.http.MediaType;
import org.springframework.stereotype.Component;
import org.springframework.web.reactive.function.client.WebClient;
import reactor.core.publisher.Mono;

@Slf4j
@Component
public class XaiApiClient extends BaseApiClient {

    private static final String CHAT_COMPLETIONS_ENDPOINT = "/v1/chat/completions";

    public XaiApiClient(WebClient xaiApiWebClient) {
        super(xaiApiWebClient);
    }

    public Mono<XaiApiResponse> getTeamNews(XaiApiRequest request) {
        validateRequiredParameter("request", request);
        validateRequiredParameter("messages", request.getMessages());

        String logContext = extractLogContext(request);

        return handleApiCall(
                webClient.post()
                        .uri(CHAT_COMPLETIONS_ENDPOINT)
                        .contentType(MediaType.APPLICATION_JSON)
                        .accept(MediaType.APPLICATION_JSON)
                        .bodyValue(request)
                        .retrieve()
                        .bodyToMono(XaiApiResponse.class)
                        .doOnSubscribe(subscription -> log.debug("Calling X.AI API for team news: {}", logContext))
                        .doOnSuccess(response -> {
                            if (response != null && response.getUsage() != null) {
                                log.debug("X.AI API call completed. Tokens used: {}, Sources used: {}, Context: {}",
                                         response.getUsage().getTotalTokens(),
                                         response.getUsage().getNumSourcesUsed(),
                                         logContext);
                            } else {
                                log.debug(TeamNewsConstants.CLIENT_LOG_MESSAGE_SUCCESS);
                            }
                        })
                        .doOnError(error -> log.error(TeamNewsConstants.CLIENT_LOG_MESSAGE_FAILURE, error.getMessage())),
                "getTeamNews"
        );
    }

    private String extractLogContext(XaiApiRequest request) {
        if (request != null && request.getMessages() != null && !request.getMessages().isEmpty()) {
            String userMessage = request.getMessages().stream()
                    .filter(msg -> "user".equals(msg.getRole()))
                    .map(XaiApiRequest.Message::getContent)
                    .findFirst()
                    .orElse("");

            // Extract team names from the user message for logging context
            if (userMessage.contains(" and ")) {
                String[] parts = userMessage.split(" and ");
                if (parts.length >= 2) {
                    String team1 = parts[0].substring(parts[0].lastIndexOf(" ") + 1);
                    String team2 = parts[1].split(" ")[0];
                    return team1 + " vs " + team2;
                }
            }
        }
        return "unknown match";
    }
}
