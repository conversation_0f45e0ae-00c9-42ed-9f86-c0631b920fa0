package com.sportal365.articlescheduler.sportsdata.twitter.teamnews.chain;

import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Component;

/**
 * Content processor that normalizes whitespace and removes excessive spacing.
 * Part of the Chain of Responsibility pattern for content processing.
 */
@Slf4j
@Component
public class WhitespaceProcessor extends ContentProcessor {
    
    @Override
    protected String doProcess(String content) {
        if (content == null || content.isEmpty()) {
            return content;
        }
        
        String processed = content;
        
        // Clean up multiple spaces and normalize whitespace
        processed = processed.replaceAll("\\s+", " ");
        
        // Remove excessive newlines
        processed = processed.replaceAll("\\n\\s*\\n", "\n");
        
        // Remove leading/trailing whitespace
        processed = processed.trim();
        
        log.debug("Normalized whitespace, trimmed content");
        
        return processed;
    }
    
    @Override
    protected boolean canProcess(String content) {
        return super.canProcess(content) && needsWhitespaceProcessing(content);
    }
    
    /**
     * Checks if the content needs whitespace processing.
     * 
     * @param content The content to check
     * @return true if whitespace processing is needed
     */
    private boolean needsWhitespaceProcessing(String content) {
        if (content == null) {
            return false;
        }
        
        // Check for multiple consecutive spaces
        if (content.contains("  ")) {
            return true;
        }
        
        // Check for multiple consecutive newlines
        if (content.contains("\n\n")) {
            return true;
        }
        
        // Check for leading/trailing whitespace
        return !content.equals(content.trim());
    }
}
