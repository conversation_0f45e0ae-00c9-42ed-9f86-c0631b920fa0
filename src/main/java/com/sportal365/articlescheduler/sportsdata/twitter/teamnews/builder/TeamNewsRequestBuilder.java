package com.sportal365.articlescheduler.sportsdata.twitter.teamnews.builder;

import com.sportal365.articlescheduler.domain.model.MatchDetails;
import com.sportal365.articlescheduler.domain.model.Schedule;
import com.sportal365.articlescheduler.sportsdata.twitter.teamnews.constants.TeamNewsConstants;
import com.sportal365.articlescheduler.sportsdata.twitter.teamnews.model.XaiApiRequest;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Component;

import java.time.LocalDate;
import java.time.format.DateTimeFormatter;
import java.util.List;

/**
 * Implementation of XaiRequestBuilder for team news requests.
 * Constructs XAI API requests with proper configuration and search parameters.
 */
@Slf4j
@Component
public class TeamNewsRequestBuilder implements XaiRequestBuilder {
    
    private MatchDetails matchDetails;
    private Schedule schedule;
    private String language;
    private PromptTemplateBuilder promptTemplateBuilder;
    
    @Override
    public XaiRequestBuilder withMatchDetails(MatchDetails matchDetails) {
        this.matchDetails = matchDetails;
        return this;
    }
    
    @Override
    public XaiRequestBuilder withSchedule(Schedule schedule) {
        this.schedule = schedule;
        return this;
    }
    
    @Override
    public XaiRequestBuilder withLanguage(String language) {
        this.language = language;
        return this;
    }
    
    @Override
    public XaiRequestBuilder withPromptTemplate(PromptTemplateBuilder promptTemplateBuilder) {
        this.promptTemplateBuilder = promptTemplateBuilder;
        return this;
    }
    
    @Override
    public XaiApiRequest build() {
        validateRequiredParameters();
        
        String userPrompt = promptTemplateBuilder.buildUserPrompt(matchDetails, schedule);
        String systemPrompt = promptTemplateBuilder.getSystemPrompt();
        
        XaiApiRequest request = XaiApiRequest.builder()
                .messages(List.of(
                        XaiApiRequest.Message.builder()
                                .role(TeamNewsConstants.SYSTEM_ROLE)
                                .content(systemPrompt)
                                .build(),
                        XaiApiRequest.Message.builder()
                                .role(TeamNewsConstants.USER_ROLE)
                                .content(userPrompt)
                                .build()
                ))
                .model(TeamNewsConstants.XAI_MODEL)
                .stream(TeamNewsConstants.DEFAULT_STREAM)
                .temperature(TeamNewsConstants.DEFAULT_TEMPERATURE)
                .maxTokens(TeamNewsConstants.DEFAULT_MAX_TOKENS)
                .searchParameters(buildSearchParameters())
                .build();
        
        log.debug("Built XAI request for match: {} vs {}", 
                 matchDetails.getHomeTeam(), matchDetails.getAwayTeam());
        
        return request;
    }
    
    @Override
    public XaiRequestBuilder reset() {
        this.matchDetails = null;
        this.schedule = null;
        this.language = null;
        this.promptTemplateBuilder = null;
        return this;
    }
    
    private void validateRequiredParameters() {
        if (matchDetails == null) {
            throw new IllegalStateException("MatchDetails is required to build XAI request");
        }
        if (promptTemplateBuilder == null) {
            throw new IllegalStateException("PromptTemplateBuilder is required to build XAI request");
        }
        if (!promptTemplateBuilder.canBuildPrompt(matchDetails, schedule)) {
            throw new IllegalStateException("PromptTemplateBuilder cannot build prompt with provided data");
        }
    }
    
    private XaiApiRequest.SearchParameters buildSearchParameters() {
        String thirtyDaysAgo = LocalDate.now().minusDays(30).format(DateTimeFormatter.ISO_LOCAL_DATE);
        
        return XaiApiRequest.SearchParameters.builder()
                .mode(TeamNewsConstants.SEARCH_MODE_ON)
                .sources(List.of(
                        XaiApiRequest.SearchParameters.Source.builder()
                                .type(TeamNewsConstants.SOURCE_TYPE_X)
                                .xHandles(TeamNewsConstants.DEFAULT_X_HANDLES)
                                .build(),
                        XaiApiRequest.SearchParameters.Source.builder()
                                .type(TeamNewsConstants.SOURCE_TYPE_WEB)
                                .build(),
                        XaiApiRequest.SearchParameters.Source.builder()
                                .type(TeamNewsConstants.SOURCE_TYPE_NEWS)
                                .build()
                ))
                .fromDate(thirtyDaysAgo)
                .returnCitations(TeamNewsConstants.DEFAULT_RETURN_CITATIONS)
                .maxSearchResults(TeamNewsConstants.DEFAULT_MAX_SEARCH_RESULTS)
                .build();
    }
}
