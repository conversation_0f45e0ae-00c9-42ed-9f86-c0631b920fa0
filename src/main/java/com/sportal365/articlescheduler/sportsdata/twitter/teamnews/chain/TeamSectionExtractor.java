package com.sportal365.articlescheduler.sportsdata.twitter.teamnews.chain;

import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Component;

/**
 * Content processor that extracts and processes team-specific sections.
 * Part of the Chain of Responsibility pattern for content processing.
 */
@Slf4j
@Component
public class TeamSectionExtractor extends ContentProcessor {
    
    @Override
    protected String doProcess(String content) {
        if (content == null || content.isEmpty()) {
            return content;
        }
        
        String processed = content;
        
        // Remove team name from the beginning if it exists (first line)
        String[] lines = processed.split("\\n", 2);
        if (lines.length > 1) {
            // Check if the first line looks like a team name header
            String firstLine = lines[0].trim();
            if (isTeamNameHeader(firstLine)) {
                processed = lines[1].trim();
                log.debug("Removed team name header: {}", firstLine);
            }
        }
        
        return processed;
    }
    
    @Override
    protected boolean canProcess(String content) {
        return super.canProcess(content) && content.contains("\n");
    }
    
    /**
     * Checks if a line appears to be a team name header.
     * 
     * @param line The line to check
     * @return true if it looks like a team name header
     */
    private boolean isTeamNameHeader(String line) {
        if (line == null || line.isEmpty()) {
            return false;
        }
        
        // Simple heuristics for team name headers
        // - Short lines (likely team names)
        // - Lines that don't end with punctuation (not sentences)
        // - Lines that don't contain common article words
        
        if (line.length() > 50) {
            return false; // Too long to be a team name
        }
        
        if (line.endsWith(".") || line.endsWith("!") || line.endsWith("?")) {
            return false; // Ends with punctuation, likely a sentence
        }
        
        // Check for common article words that indicate it's not a team name
        String lowerLine = line.toLowerCase();
        String[] articleWords = {"the", "a", "an", "in", "on", "at", "for", "with", "by", "from"};
        for (String word : articleWords) {
            if (lowerLine.contains(" " + word + " ") || lowerLine.startsWith(word + " ")) {
                return false;
            }
        }
        
        return true;
    }
}
