package com.sportal365.articlescheduler.sportsdata.twitter.teamnews.builder;

import com.sportal365.articlescheduler.domain.model.MatchDetails;
import com.sportal365.articlescheduler.domain.model.Schedule;

/**
 * Interface for building prompt templates for AI API requests.
 * Follows the Builder pattern to construct complex prompts from match and schedule data.
 */
public interface PromptTemplateBuilder {
    
    /**
     * Builds a user prompt based on match details and schedule information.
     * 
     * @param matchDetails The match details containing team, venue, and other information
     * @param schedule The schedule containing language and other configuration
     * @return The constructed user prompt string
     */
    String buildUserPrompt(MatchDetails matchDetails, Schedule schedule);
    
    /**
     * Gets the system prompt for this template builder.
     * 
     * @return The system prompt string
     */
    String getSystemPrompt();
    
    /**
     * Validates that the required data is available for prompt building.
     * 
     * @param matchDetails The match details to validate
     * @param schedule The schedule to validate
     * @return true if data is sufficient for prompt building, false otherwise
     */
    boolean canBuildPrompt(MatchDetails matchDetails, Schedule schedule);
    
    /**
     * Gets the template name for logging and identification purposes.
     * 
     * @return The template name
     */
    String getTemplateName();
}
