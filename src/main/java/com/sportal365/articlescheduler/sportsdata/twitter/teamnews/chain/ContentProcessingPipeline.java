package com.sportal365.articlescheduler.sportsdata.twitter.teamnews.chain;

import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Component;

import java.util.List;

/**
 * Content processing pipeline that orchestrates the Chain of Responsibility pattern.
 * Configures and executes the chain of content processors in the correct order.
 */
@Slf4j
@Component
@RequiredArgsConstructor
public class ContentProcessingPipeline {
    
    private final MarkdownHeaderProcessor markdownHeaderProcessor;
    private final SpecialSymbolProcessor specialSymbolProcessor;
    private final WhitespaceProcessor whitespaceProcessor;
    private final TeamSectionExtractor teamSectionExtractor;
    
    /**
     * Processes content through the complete pipeline.
     * 
     * @param content The content to process
     * @return The fully processed content
     */
    public String processContent(String content) {
        if (content == null || content.isEmpty()) {
            log.debug("Empty content provided to pipeline");
            return content;
        }
        
        log.debug("Starting content processing pipeline for content length: {}", content.length());
        
        ContentProcessor pipeline = buildPipeline();
        String result = pipeline.process(content);
        
        log.debug("Content processing pipeline completed, result length: {}", result.length());
        return result;
    }
    
    /**
     * Processes content with a custom pipeline configuration.
     * 
     * @param content The content to process
     * @param processors The list of processors to use in order
     * @return The processed content
     */
    public String processContentWithCustomPipeline(String content, List<ContentProcessor> processors) {
        if (content == null || content.isEmpty()) {
            return content;
        }
        
        if (processors == null || processors.isEmpty()) {
            log.warn("No processors provided, returning original content");
            return content;
        }
        
        log.debug("Starting custom content processing pipeline with {} processors", processors.size());
        
        ContentProcessor pipeline = buildCustomPipeline(processors);
        String result = pipeline.process(content);
        
        log.debug("Custom content processing pipeline completed");
        return result;
    }
    
    /**
     * Builds the default content processing pipeline.
     * Order: Headers -> Special Symbols -> Team Sections -> Whitespace
     * 
     * @return The head of the processing chain
     */
    private ContentProcessor buildPipeline() {
        // Build the chain in the correct order
        markdownHeaderProcessor
                .setNext(specialSymbolProcessor)
                .setNext(teamSectionExtractor)
                .setNext(whitespaceProcessor);
        
        log.debug("Built default content processing pipeline: {} -> {} -> {} -> {}", 
                 markdownHeaderProcessor.getProcessorName(),
                 specialSymbolProcessor.getProcessorName(),
                 teamSectionExtractor.getProcessorName(),
                 whitespaceProcessor.getProcessorName());
        
        return markdownHeaderProcessor;
    }
    
    /**
     * Builds a custom content processing pipeline from the provided processors.
     * 
     * @param processors The list of processors to chain together
     * @return The head of the processing chain
     */
    private ContentProcessor buildCustomPipeline(List<ContentProcessor> processors) {
        if (processors.size() == 1) {
            return processors.get(0);
        }
        
        ContentProcessor head = processors.get(0);
        ContentProcessor current = head;
        
        for (int i = 1; i < processors.size(); i++) {
            current = current.setNext(processors.get(i));
        }
        
        log.debug("Built custom content processing pipeline with {} processors", processors.size());
        return head;
    }
    
    /**
     * Gets the available processors for custom pipeline configuration.
     * 
     * @return List of available processors
     */
    public List<ContentProcessor> getAvailableProcessors() {
        return List.of(
                markdownHeaderProcessor,
                specialSymbolProcessor,
                whitespaceProcessor,
                teamSectionExtractor
        );
    }
}
