package com.sportal365.articlescheduler.sportsdata.twitter.teamnews.builder;

import com.sportal365.articlescheduler.domain.model.MatchDetails;
import com.sportal365.articlescheduler.domain.model.Schedule;
import com.sportal365.articlescheduler.sportsdata.twitter.teamnews.constants.TeamNewsConstants;
import com.sportal365.articlescheduler.sportsdata.twitter.teamnews.service.DateFormattingService;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Component;

/**
 * Implementation of PromptTemplateBuilder for team news prompts.
 * Constructs comprehensive sports journalism prompts with match details.
 */
@Slf4j
@Component
@RequiredArgsConstructor
public class TeamNewsPromptTemplate implements PromptTemplateBuilder {
    
    private final DateFormattingService dateFormattingService;
    
    @Override
    public String buildUserPrompt(MatchDetails matchDetails, Schedule schedule) {
        if (!canBuildPrompt(matchDetails, schedule)) {
            throw new IllegalArgumentException("Insufficient data to build team news prompt");
        }
        
        // Extract basic match information with fallbacks
        String homeTeam = extractValue(matchDetails.getHomeTeam(), "Home Team");
        String awayTeam = extractValue(matchDetails.getAwayTeam(), "Away Team");
        String tournament = extractValue(matchDetails.getTournamentName(), "Tournament");
        String matchDate = dateFormattingService.formatMatchDate(matchDetails.getDate(), matchDetails.getTime());
        String venue = extractValue(matchDetails.getVenue(), "TBD Venue");
        String referee = extractValue(matchDetails.getReferee(), "TBD Referee");
        String round = extractValue(matchDetails.getRound(), "Regular Round");
        String country = extractValue(matchDetails.getCountry(), "International");
        String sport = extractValue(matchDetails.getSport(), "Football");
        String language = schedule != null && schedule.getArticleLanguage() != null ?
                         schedule.getArticleLanguage() : "english";

        String userPrompt = String.format(
                TeamNewsConstants.USER_PROMPT_TEMPLATE,
                sport,           // 1. Sport (%s)
                homeTeam,        // 2. Home team (%s)
                awayTeam,        // 3. Away team (%s)
                tournament,      // 4. Tournament (%s)
                matchDate,       // 5. Date (%s)
                venue,           // 6. Venue (%s)
                referee,         // 7. Referee (%s)
                round,           // 8. Round (%s)
                country,         // 9. Country (%s)
                sport,           // 10. Sport (%s) - for "sport of %s"
                language,        // 11. Language (%s)
                homeTeam,        // 12. Home team (%s) - for "### %s News"
                awayTeam,        // 13. Away team (%s) - for "### %s News"
                homeTeam,        // 14. Home team (%s) - for "### %s Manager"
                awayTeam,        // 15. Away team (%s) - for "### %s Manager"
                homeTeam,        // 16. Home team (%s) - for "### %s Players"
                awayTeam         // 17. Away team (%s) - for "### %s Players"
        );
        
        log.debug("Built team news prompt for match: {} vs {}", homeTeam, awayTeam);
        return userPrompt;
    }
    
    @Override
    public String getSystemPrompt() {
        return TeamNewsConstants.SYSTEM_PROMPT;
    }
    
    @Override
    public boolean canBuildPrompt(MatchDetails matchDetails, Schedule schedule) {
        return matchDetails != null && 
               (matchDetails.getHomeTeam() != null || matchDetails.getAwayTeam() != null);
    }
    
    @Override
    public String getTemplateName() {
        return "TeamNewsPromptTemplate";
    }
    
    /**
     * Extracts a value with a fallback if the value is null or empty.
     * 
     * @param value The value to extract
     * @param fallback The fallback value if the original is null or empty
     * @return The extracted value or fallback
     */
    private String extractValue(String value, String fallback) {
        return value != null && !value.trim().isEmpty() ? value.trim() : fallback;
    }
}
