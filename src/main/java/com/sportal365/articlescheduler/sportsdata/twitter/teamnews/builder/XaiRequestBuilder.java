package com.sportal365.articlescheduler.sportsdata.twitter.teamnews.builder;

import com.sportal365.articlescheduler.domain.model.MatchDetails;
import com.sportal365.articlescheduler.domain.model.Schedule;
import com.sportal365.articlescheduler.sportsdata.twitter.teamnews.model.XaiApiRequest;

/**
 * Builder interface for constructing XAI API requests.
 * Follows the Builder pattern for complex API request construction.
 */
public interface XaiRequestBuilder {
    
    /**
     * Sets the match details for the request.
     * 
     * @param matchDetails The match details
     * @return This builder instance for method chaining
     */
    XaiRequestBuilder withMatchDetails(MatchDetails matchDetails);
    
    /**
     * Sets the schedule information for the request.
     * 
     * @param schedule The schedule information
     * @return This builder instance for method chaining
     */
    XaiRequestBuilder withSchedule(Schedule schedule);
    
    /**
     * Sets the language for the request.
     * 
     * @param language The target language
     * @return This builder instance for method chaining
     */
    XaiRequestBuilder withLanguage(String language);
    
    /**
     * Sets the prompt template builder to use.
     * 
     * @param promptTemplateBuilder The prompt template builder
     * @return This builder instance for method chaining
     */
    XaiRequestBuilder withPromptTemplate(PromptTemplateBuilder promptTemplateBuilder);
    
    /**
     * Builds the XAI API request with all configured parameters.
     * 
     * @return The constructed XaiApiRequest
     * @throws IllegalStateException if required parameters are missing
     */
    XaiApiRequest build();
    
    /**
     * Resets the builder to its initial state.
     * 
     * @return This builder instance for reuse
     */
    XaiRequestBuilder reset();
}
