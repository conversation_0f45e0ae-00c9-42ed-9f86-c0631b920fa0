package com.sportal365.articlescheduler.sportsdata.twitter.teamnews.chain;

import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Component;

/**
 * Content processor that handles Markdown headers (##, ###, etc.).
 * Part of the Chain of Responsibility pattern for content processing.
 */
@Slf4j
@Component
public class MarkdownHeaderProcessor extends ContentProcessor {
    
    @Override
    protected String doProcess(String content) {
        if (content == null || content.isEmpty()) {
            return content;
        }
        
        // Remove markdown headers (### and ##)
        String processed = content.replaceAll("(?m)^#{1,6}\\s*", "");
        
        log.debug("Processed markdown headers, removed {} header markers", 
                 countHeaderMarkers(content) - countHeaderMarkers(processed));
        
        return processed;
    }
    
    @Override
    protected boolean canProcess(String content) {
        return super.canProcess(content) && content.contains("#");
    }
    
    /**
     * Counts the number of header markers in the content.
     * 
     * @param content The content to analyze
     * @return The number of header markers found
     */
    private int countHeaderMarkers(String content) {
        if (content == null) {
            return 0;
        }
        
        int count = 0;
        String[] lines = content.split("\n");
        for (String line : lines) {
            if (line.trim().matches("^#{1,6}\\s*.*")) {
                count++;
            }
        }
        return count;
    }
}
