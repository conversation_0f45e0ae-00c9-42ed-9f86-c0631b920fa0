package com.sportal365.articlescheduler.sportsdata.twitter.teamnews.template;

import com.sportal365.articlescheduler.domain.model.MatchDetails;
import com.sportal365.articlescheduler.domain.model.Schedule;
import com.sportal365.articlescheduler.sportsdata.match.MatchDetailEnrichService;
import lombok.extern.slf4j.Slf4j;

/**
 * Abstract base class implementing the Template Method pattern for match detail enrichment.
 * Defines the standard enrichment process while allowing subclasses to customize specific steps.
 */
@Slf4j
public abstract class AbstractEnrichmentService implements MatchDetailEnrichService {
    
    @Override
    public final MatchDetails enrich(MatchDetails matchDetails, Schedule schedule) {
        try {
            log.info("Starting enrichment process for match: {}", getMatchIdentifier(matchDetails));
            
            // Step 1: Validate input
            if (!validateInput(matchDetails, schedule)) {
                log.warn("Input validation failed for match: {}", getMatchIdentifier(matchDetails));
                return matchDetails;
            }
            
            // Step 2: Fetch data
            Object rawData = fetchData(matchDetails, schedule);
            if (rawData == null) {
                log.warn("No data fetched for match: {}", getMatchIdentifier(matchDetails));
                return matchDetails;
            }
            
            // Step 3: Process data
            Object processedData = processData(rawData);
            if (processedData == null) {
                log.warn("Data processing failed for match: {}", getMatchIdentifier(matchDetails));
                return matchDetails;
            }
            
            // Step 4: Map to match details
            MatchDetails enrichedMatch = mapToMatchDetails(matchDetails, processedData);
            
            log.info("Enrichment completed successfully for match: {}", getMatchIdentifier(matchDetails));
            return enrichedMatch;
            
        } catch (Exception e) {
            log.error("Error during enrichment for match: {}. Error: {}", 
                     getMatchIdentifier(matchDetails), e.getMessage(), e);
            return handleError(e, matchDetails);
        }
    }
    
    /**
     * Validates the input parameters before processing.
     * 
     * @param matchDetails The match details to validate
     * @param schedule The schedule to validate
     * @return true if input is valid, false otherwise
     */
    protected abstract boolean validateInput(MatchDetails matchDetails, Schedule schedule);
    
    /**
     * Fetches the raw data needed for enrichment.
     * 
     * @param matchDetails The match details
     * @param schedule The schedule
     * @return The fetched raw data, or null if fetching fails
     */
    protected abstract Object fetchData(MatchDetails matchDetails, Schedule schedule);
    
    /**
     * Processes the raw data into a format suitable for mapping.
     * 
     * @param rawData The raw data to process
     * @return The processed data, or null if processing fails
     */
    protected abstract Object processData(Object rawData);
    
    /**
     * Maps the processed data to an enriched MatchDetails object.
     * 
     * @param originalMatchDetails The original match details
     * @param processedData The processed data
     * @return The enriched match details
     */
    protected abstract MatchDetails mapToMatchDetails(MatchDetails originalMatchDetails, Object processedData);
    
    /**
     * Handles errors that occur during the enrichment process.
     * Default implementation returns the original match details.
     * 
     * @param exception The exception that occurred
     * @param originalMatchDetails The original match details
     * @return The match details to return (usually the original)
     */
    protected MatchDetails handleError(Exception exception, MatchDetails originalMatchDetails) {
        log.error("Handling enrichment error: {}", exception.getMessage());
        return originalMatchDetails;
    }
    
    /**
     * Gets a string identifier for the match for logging purposes.
     * 
     * @param matchDetails The match details
     * @return A string identifier for the match
     */
    protected String getMatchIdentifier(MatchDetails matchDetails) {
        if (matchDetails == null) {
            return "unknown";
        }
        
        String matchId = matchDetails.getMatchId();
        if (matchId != null && !matchId.isEmpty()) {
            return matchId;
        }
        
        String homeTeam = matchDetails.getHomeTeam();
        String awayTeam = matchDetails.getAwayTeam();
        if (homeTeam != null && awayTeam != null) {
            return homeTeam + " vs " + awayTeam;
        }
        
        return "unknown match";
    }
    
    /**
     * Gets the name of this enrichment service for logging and identification.
     * 
     * @return The service name
     */
    protected String getServiceName() {
        return this.getClass().getSimpleName();
    }
}
