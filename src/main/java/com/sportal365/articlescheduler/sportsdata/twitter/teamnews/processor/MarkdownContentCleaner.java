package com.sportal365.articlescheduler.sportsdata.twitter.teamnews.processor;

import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Component;

import java.util.regex.Pattern;

/**
 * Implementation of ContentCleaningStrategy for Markdown content.
 * Removes Markdown formatting, special symbols, and normalizes whitespace.
 */
@Slf4j
@Component
public class MarkdownContentCleaner implements ContentCleaningStrategy {
    
    // Pattern to match special symbols like **, ###, =, etc.
    private static final Pattern SPECIAL_SYMBOLS_PATTERN = Pattern.compile("[*#=\\-_`~]+");
    
    @Override
    public String cleanContent(String content) {
        if (content == null || content.isEmpty()) {
            return "";
        }

        // Remove special symbols
        String cleaned = SPECIAL_SYMBOLS_PATTERN.matcher(content).replaceAll("");

        // Clean up multiple spaces and newlines
        cleaned = cleaned.replaceAll("\\s+", " ");

        // Remove leading/trailing whitespace
        cleaned = cleaned.trim();

        // Remove team name from the beginning if it exists (first line)
        String[] lines = cleaned.split("\\n", 2);
        if (lines.length > 1) {
            cleaned = lines[1].trim();
        }

        return cleaned;
    }
    
    @Override
    public String cleanAllContent(String content) {
        if (content == null || content.isEmpty()) {
            return "";
        }

        // Remove special symbols: **, ###, =, ---, -, *, _, `, ~, etc.
        String cleaned = content;

        // Remove markdown headers (### and ##)
        cleaned = cleaned.replaceAll("(?m)^#{1,6}\\s*", "");

        // Remove bold/italic markers
        cleaned = cleaned.replaceAll("\\*{1,2}([^*]+)\\*{1,2}", "$1");

        // Remove bullet points and dashes
        cleaned = cleaned.replaceAll("(?m)^\\s*[-*+]\\s*", "");

        // Remove horizontal rules
        cleaned = cleaned.replaceAll("(?m)^\\s*[-=]{3,}\\s*$", "");

        // Remove underscores used for emphasis
        cleaned = cleaned.replaceAll("_{1,2}([^_]+)_{1,2}", "$1");

        // Remove backticks
        cleaned = cleaned.replaceAll("`([^`]+)`", "$1");

        // Clean up multiple spaces and normalize whitespace
        cleaned = cleaned.replaceAll("\\s+", " ");

        // Remove excessive newlines
        cleaned = cleaned.replaceAll("\\n\\s*\\n", "\n");

        // Remove leading/trailing whitespace
        cleaned = cleaned.trim();

        return cleaned;
    }
    
    @Override
    public String getStrategyName() {
        return "MarkdownContentCleaner";
    }
}
