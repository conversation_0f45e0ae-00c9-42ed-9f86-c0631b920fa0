package com.sportal365.articlescheduler.sportsdata.twitter.teamnews.interfaces;

import java.util.Map;

/**
 * Focused interface for prompt building operations.
 * Segregated from other template and request building concerns.
 */
public interface PromptBuilder {
    
    /**
     * Builds a prompt from the given parameters.
     * 
     * @param parameters The parameters to use in prompt building
     * @return The built prompt
     */
    String buildPrompt(Map<String, Object> parameters);
    
    /**
     * Builds a prompt with validation.
     * 
     * @param parameters The parameters to use
     * @param validate Whether to validate the parameters
     * @return The built prompt
     * @throws IllegalArgumentException if validation fails
     */
    String buildPrompt(Map<String, Object> parameters, boolean validate);
    
    /**
     * Validates the parameters for prompt building.
     * 
     * @param parameters The parameters to validate
     * @return Validation result
     */
    ValidationResult validateParameters(Map<String, Object> parameters);
    
    /**
     * Gets the required parameters for this prompt builder.
     * 
     * @return Array of required parameter names
     */
    String[] getRequiredParameters();
    
    /**
     * Gets the optional parameters for this prompt builder.
     * 
     * @return Array of optional parameter names
     */
    String[] getOptionalParameters();
    
    /**
     * Gets the prompt template used by this builder.
     * 
     * @return The prompt template
     */
    String getTemplate();
    
    /**
     * Result of parameter validation.
     */
    interface ValidationResult {
        /**
         * Whether the validation passed.
         * 
         * @return true if validation passed
         */
        boolean isValid();
        
        /**
         * Gets validation error messages.
         * 
         * @return Array of error messages, empty if valid
         */
        String[] getErrors();
        
        /**
         * Gets missing required parameters.
         * 
         * @return Array of missing parameter names
         */
        String[] getMissingParameters();
        
        /**
         * Gets invalid parameter values.
         * 
         * @return Map of parameter name to error message
         */
        Map<String, String> getInvalidParameters();
    }
}
