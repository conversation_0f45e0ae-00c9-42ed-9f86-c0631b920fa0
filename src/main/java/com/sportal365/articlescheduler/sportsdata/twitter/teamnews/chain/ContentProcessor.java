package com.sportal365.articlescheduler.sportsdata.twitter.teamnews.chain;

import lombok.extern.slf4j.Slf4j;

/**
 * Abstract base class for content processors implementing Chain of Responsibility pattern.
 * Each processor handles a specific aspect of content processing and can pass the content
 * to the next processor in the chain.
 */
@Slf4j
public abstract class ContentProcessor {
    
    private ContentProcessor nextProcessor;
    
    /**
     * Sets the next processor in the chain.
     * 
     * @param processor The next processor
     * @return The next processor for method chaining
     */
    public ContentProcessor setNext(ContentProcessor processor) {
        this.nextProcessor = processor;
        return processor;
    }
    
    /**
     * Processes the content and passes it to the next processor in the chain.
     * 
     * @param content The content to process
     * @return The processed content
     */
    public String process(String content) {
        log.debug("Processing content with {}", this.getClass().getSimpleName());
        
        // Process content with this processor
        String processedContent = doProcess(content);
        
        // Pass to next processor if available
        if (nextProcessor != null) {
            return nextProcessor.process(processedContent);
        }
        
        return processedContent;
    }
    
    /**
     * Abstract method that each concrete processor must implement.
     * Contains the specific processing logic for this processor.
     * 
     * @param content The content to process
     * @return The processed content
     */
    protected abstract String doProcess(String content);
    
    /**
     * Gets the name of this processor for logging and identification.
     * 
     * @return The processor name
     */
    public String getProcessorName() {
        return this.getClass().getSimpleName();
    }
    
    /**
     * Checks if this processor can handle the given content.
     * Default implementation returns true (all processors can handle any content).
     * Override in specific processors for content-type specific handling.
     * 
     * @param content The content to check
     * @return true if this processor can handle the content
     */
    protected boolean canProcess(String content) {
        return content != null;
    }
}
