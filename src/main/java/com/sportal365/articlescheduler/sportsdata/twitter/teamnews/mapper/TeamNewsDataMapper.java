package com.sportal365.articlescheduler.sportsdata.twitter.teamnews.mapper;

import com.sportal365.articlescheduler.domain.model.MatchDetails;
import com.sportal365.articlescheduler.sportsdata.twitter.teamnews.model.TeamNewsData;
import com.sportal365.articlescheduler.sportsdata.twitter.teamnews.processor.ContentParsingStrategy;
import com.sportal365.articlescheduler.sportsdata.twitter.teamnews.processor.ContentCleaningStrategy;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Component;

/**
 * Implementation of ResponseToDomainMapper for team news data.
 * Maps API response data to domain objects using content processing strategies.
 */
@Slf4j
@Component
@RequiredArgsConstructor
public class TeamNewsDataMapper implements ResponseToDomainMapper {
    
    private final ContentParsingStrategy contentParsingStrategy;
    private final ContentCleaningStrategy contentCleaningStrategy;
    
    @Override
    public MatchDetails.TeamNews mapToMatchDetailsTeamNews(TeamNewsData teamNewsData) {
        try {
            if (!canMapData(teamNewsData)) {
                return null;
            }

            String content = teamNewsData.getMarkdownContent();
            
            // Extract the first ## section (Team News)
            String teamNewsContent = contentParsingStrategy.extractSectionContent(content, 0);

            if (teamNewsContent == null || teamNewsContent.trim().isEmpty()) {
                log.debug("No team news content found for match: {}", teamNewsData.getMatchId());
                return null;
            }

            // Clean the content by removing special symbols
            String cleanedContent = contentCleaningStrategy.cleanAllContent(teamNewsContent.trim());

            return MatchDetails.TeamNews.builder()
                    .teamNews(cleanedContent)
                    .build();

        } catch (Exception e) {
            log.error("Error mapping team news data for match {}: {}", 
                     teamNewsData.getMatchId(), e.getMessage(), e);
            return null;
        }
    }
    
    @Override
    public MatchDetails.Quotes mapToMatchDetailsQuotes(TeamNewsData teamNewsData) {
        try {
            if (!canMapData(teamNewsData)) {
                return null;
            }

            String content = teamNewsData.getMarkdownContent();

            // Extract the second ## section (Manager Quotes)
            String managersQuotesContent = contentParsingStrategy.extractSectionContent(content, 1);

            // Extract the third ## section (Player Quotes)
            String playersQuotesContent = contentParsingStrategy.extractSectionContent(content, 2);

            // Return null if both quotes are empty
            if ((managersQuotesContent == null || managersQuotesContent.trim().isEmpty()) &&
                    (playersQuotesContent == null || playersQuotesContent.trim().isEmpty())) {
                log.debug("No quotes content found for match: {}", teamNewsData.getMatchId());
                return null;
            }

            // Clean the quotes content by removing special symbols
            String cleanedManagersQuotes = managersQuotesContent != null ? 
                    contentCleaningStrategy.cleanAllContent(managersQuotesContent.trim()) : null;
            String cleanedPlayersQuotes = playersQuotesContent != null ? 
                    contentCleaningStrategy.cleanAllContent(playersQuotesContent.trim()) : null;

            return MatchDetails.Quotes.builder()
                    .managersQuotes(cleanedManagersQuotes)
                    .playersQuotes(cleanedPlayersQuotes)
                    .build();

        } catch (Exception e) {
            log.error("Error mapping quotes data for match {}: {}", 
                     teamNewsData.getMatchId(), e.getMessage(), e);
            return null;
        }
    }
    
    @Override
    public boolean canMapData(TeamNewsData teamNewsData) {
        return teamNewsData != null && 
               teamNewsData.getMarkdownContent() != null && 
               !teamNewsData.getMarkdownContent().isEmpty() &&
               contentParsingStrategy.canHandle(teamNewsData.getMarkdownContent());
    }
    
    @Override
    public String getMapperName() {
        return "TeamNewsDataMapper";
    }
}
