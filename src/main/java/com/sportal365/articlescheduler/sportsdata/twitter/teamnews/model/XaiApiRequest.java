package com.sportal365.articlescheduler.sportsdata.twitter.teamnews.model;

import com.fasterxml.jackson.annotation.JsonProperty;
import lombok.Builder;
import lombok.Data;

import java.util.List;

@Data
@Builder
public class XaiApiRequest {

    private List<Message> messages;
    private String model;
    private boolean stream;
    private int temperature;
    @JsonProperty("max_tokens")
    private int maxTokens;
    @JsonProperty("search_parameters")
    private SearchParameters searchParameters;

    @Data
    @Builder
    public static class Message {
        private String role;
        private String content;
    }

    @Data
    @Builder
    public static class SearchParameters {
        private String mode;
        private List<Source> sources;
        @JsonProperty("from_date")
        private String fromDate;
        @JsonProperty("return_citations")
        private boolean returnCitations;
        @JsonProperty("max_search_results")
        private int maxSearchResults;

        @Data
        @Builder
        public static class Source {
            private String type;
            @JsonProperty("x_handles")
            private List<String> xHandles;
        }
    }
}
