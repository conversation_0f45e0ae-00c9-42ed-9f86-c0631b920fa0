package com.sportal365.articlescheduler.sportsdata.twitter.teamnews.processor;

/**
 * Strategy interface for cleaning content by removing formatting and special symbols.
 * Follows the Strategy pattern to allow different cleaning approaches.
 */
public interface ContentCleaningStrategy {
    
    /**
     * Cleans content by removing unwanted formatting and symbols.
     * 
     * @param content The content to clean
     * @return Cleaned content
     */
    String cleanContent(String content);
    
    /**
     * Performs comprehensive cleaning of all content including special symbols.
     * 
     * @param content The content to clean thoroughly
     * @return Thoroughly cleaned content
     */
    String cleanAllContent(String content);
    
    /**
     * Gets the name of this cleaning strategy for logging and configuration.
     * 
     * @return Strategy name
     */
    String getStrategyName();
}
