package com.sportal365.articlescheduler.sportsdata.twitter.teamnews.model;

import lombok.Builder;
import lombok.Data;

import java.util.List;

@Data
@Builder
public class TeamNewsData {

    private String matchId;
    private String homeTeam;
    private String awayTeam;
    private String competition;
    private String matchDate;
    private String markdownContent;
    private List<String> citations;
    private TokenUsage tokenUsage;
    private int sourcesUsed;

    @Data
    @Builder
    public static class TokenUsage {
        private int promptTokens;
        private int completionTokens;
        private int totalTokens;
    }
}
