package com.sportal365.articlescheduler.sportsdata.twitter.teamnews.model;

import com.fasterxml.jackson.annotation.JsonProperty;
import lombok.Data;

import java.util.List;

@Data
public class XaiApiResponse {

    private String id;
    private String object;
    private long created;
    private String model;
    private List<Choice> choices;
    private Usage usage;
    @JsonProperty("system_fingerprint")
    private String systemFingerprint;
    private List<String> citations;

    @Data
    public static class Choice {
        private int index;
        private Message message;
        @JsonProperty("finish_reason")
        private String finishReason;

        @Data
        public static class Message {
            private String role;
            private String content;
            private String refusal;
        }
    }

    @Data
    public static class Usage {
        @JsonProperty("prompt_tokens")
        private int promptTokens;
        @JsonProperty("completion_tokens")
        private int completionTokens;
        @JsonProperty("total_tokens")
        private int totalTokens;
        @JsonProperty("prompt_tokens_details")
        private TokenDetails promptTokensDetails;
        @JsonProperty("completion_tokens_details")
        private TokenDetails completionTokensDetails;
        @JsonProperty("num_sources_used")
        private int numSourcesUsed;

        @Data
        public static class TokenDetails {
            @JsonProperty("text_tokens")
            private int textTokens;
            @JsonProperty("audio_tokens")
            private int audioTokens;
            @JsonProperty("image_tokens")
            private int imageTokens;
            @JsonProperty("cached_tokens")
            private int cachedTokens;
            @JsonProperty("reasoning_tokens")
            private int reasoningTokens;
            @JsonProperty("accepted_prediction_tokens")
            private int acceptedPredictionTokens;
            @JsonProperty("rejected_prediction_tokens")
            private int rejectedPredictionTokens;
        }
    }
}
