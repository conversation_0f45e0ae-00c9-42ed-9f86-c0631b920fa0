package com.sportal365.articlescheduler.sportsdata.twitter.teamnews.strategy;

import com.sportal365.articlescheduler.sportsdata.twitter.teamnews.processor.ContentParsingStrategy;
import com.sportal365.articlescheduler.sportsdata.twitter.teamnews.processor.ContentCleaningStrategy;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Component;

import java.util.List;

/**
 * Strategy selector that chooses the appropriate content processing strategies
 * based on content type and format. Implements the Strategy pattern.
 */
@Slf4j
@Component
@RequiredArgsConstructor
public class ContentProcessingStrategySelector {
    
    private final List<ContentParsingStrategy> parsingStrategies;
    private final List<ContentCleaningStrategy> cleaningStrategies;
    
    /**
     * Selects the most appropriate parsing strategy for the given content.
     * 
     * @param content The content to analyze
     * @return The best parsing strategy, or the first available if none match
     */
    public ContentParsingStrategy selectParsingStrategy(String content) {
        if (content == null || content.isEmpty()) {
            log.warn("Empty content provided, using default parsing strategy");
            return getDefaultParsingStrategy();
        }
        
        for (ContentParsingStrategy strategy : parsingStrategies) {
            if (strategy.canHandle(content)) {
                log.debug("Selected parsing strategy: {}", strategy.getClass().getSimpleName());
                return strategy;
            }
        }
        
        log.warn("No suitable parsing strategy found for content, using default");
        return getDefaultParsingStrategy();
    }
    
    /**
     * Selects the most appropriate cleaning strategy for the given content type.
     * 
     * @param contentType The type of content (e.g., "markdown", "html", "plain")
     * @return The best cleaning strategy, or the first available if none match
     */
    public ContentCleaningStrategy selectCleaningStrategy(String contentType) {
        if (contentType == null || contentType.isEmpty()) {
            return getDefaultCleaningStrategy();
        }
        
        // For now, we'll use a simple strategy selection based on content type
        // This can be extended with more sophisticated logic
        for (ContentCleaningStrategy strategy : cleaningStrategies) {
            if (isStrategyCompatible(strategy, contentType)) {
                log.debug("Selected cleaning strategy: {} for content type: {}", 
                         strategy.getStrategyName(), contentType);
                return strategy;
            }
        }
        
        log.debug("Using default cleaning strategy for content type: {}", contentType);
        return getDefaultCleaningStrategy();
    }
    
    /**
     * Gets the default parsing strategy (first in the list).
     * 
     * @return The default parsing strategy
     */
    public ContentParsingStrategy getDefaultParsingStrategy() {
        if (parsingStrategies.isEmpty()) {
            throw new IllegalStateException("No parsing strategies available");
        }
        return parsingStrategies.get(0);
    }
    
    /**
     * Gets the default cleaning strategy (first in the list).
     * 
     * @return The default cleaning strategy
     */
    public ContentCleaningStrategy getDefaultCleaningStrategy() {
        if (cleaningStrategies.isEmpty()) {
            throw new IllegalStateException("No cleaning strategies available");
        }
        return cleaningStrategies.get(0);
    }
    
    /**
     * Checks if a cleaning strategy is compatible with the given content type.
     * 
     * @param strategy The cleaning strategy to check
     * @param contentType The content type
     * @return true if compatible, false otherwise
     */
    private boolean isStrategyCompatible(ContentCleaningStrategy strategy, String contentType) {
        String strategyName = strategy.getStrategyName().toLowerCase();
        String type = contentType.toLowerCase();
        
        // Simple compatibility check - can be enhanced with more sophisticated logic
        return strategyName.contains(type) || 
               (type.contains("markdown") && strategyName.contains("markdown")) ||
               (type.contains("html") && strategyName.contains("html"));
    }
}
