package com.sportal365.articlescheduler.sportsdata.twitter.teamnews.service;

import com.sportal365.articlescheduler.domain.model.MatchDetails;
import com.sportal365.articlescheduler.domain.model.Schedule;
import com.sportal365.articlescheduler.sportsdata.twitter.teamnews.client.XaiApiClient;
import com.sportal365.articlescheduler.sportsdata.twitter.teamnews.mapper.TeamNewsMapper;
import com.sportal365.articlescheduler.sportsdata.twitter.teamnews.mapper.TeamNewsResponseMapper;
import com.sportal365.articlescheduler.sportsdata.twitter.teamnews.model.TeamNewsData;
import com.sportal365.articlescheduler.sportsdata.twitter.teamnews.model.XaiApiRequest;
import com.sportal365.articlescheduler.sportsdata.twitter.teamnews.template.AbstractEnrichmentService;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Service;
import reactor.core.publisher.Mono;

@Slf4j
@Service
@RequiredArgsConstructor
public class TeamNewsService extends AbstractEnrichmentService {

    private final XaiApiClient xaiApiClient;
    private final TeamNewsMapper teamNewsMapper;
    private final TeamNewsResponseMapper responseMapper;

    @Override
    protected boolean validateInput(MatchDetails matchDetails, Schedule schedule) {
        if (matchDetails == null) {
            log.warn("MatchDetails is null");
            return false;
        }

        if (matchDetails.getHomeTeam() == null && matchDetails.getAwayTeam() == null) {
            log.warn("Both home and away teams are null for match: {}", matchDetails.getMatchId());
            return false;
        }

        return true;
    }

    @Override
    protected Object fetchData(MatchDetails matchDetails, Schedule schedule) {
        try {
            return getTeamNews(matchDetails, schedule).block();
        } catch (Exception e) {
            log.error("Error fetching team news data: {}", e.getMessage(), e);
            return null;
        }
    }

    @Override
    protected Object processData(Object rawData) {
        // Data is already processed by the API client and mappers
        return rawData;
    }

    @Override
    protected MatchDetails mapToMatchDetails(MatchDetails originalMatchDetails, Object processedData) {
        if (!(processedData instanceof TeamNewsData)) {
            log.warn("Processed data is not of type TeamNewsData");
            return originalMatchDetails;
        }

        TeamNewsData teamNewsData = (TeamNewsData) processedData;

        MatchDetails.TeamNews teamNews = responseMapper.mapToMatchDetailsTeamNews(teamNewsData);
        MatchDetails.Quotes quotes = responseMapper.mapToMatchDetailsQuotes(teamNewsData);

        return originalMatchDetails.toBuilder()
                .teamNews(teamNews)
                .quotes(quotes)
                .build();
    }

    private Mono<TeamNewsData> getTeamNews(MatchDetails matchDetails, Schedule schedule) {
        log.info("Fetching team news for match: {} vs {}",
                  matchDetails.getHomeTeam(),
                  matchDetails.getAwayTeam());

        XaiApiRequest request = teamNewsMapper.mapToXaiRequest(matchDetails, schedule);

        return xaiApiClient.getTeamNews(request)
                .map(response -> teamNewsMapper.mapToTeamNewsData(matchDetails, response))
                .doOnSuccess(teamNews -> log.debug("Successfully retrieved team news for match: {}", matchDetails.getMatchId()))
                .doOnError(error -> log.error("Error retrieving team news for match: {}. Error: {}",
                                             matchDetails.getMatchId(), error.getMessage(), error));
    }
}
