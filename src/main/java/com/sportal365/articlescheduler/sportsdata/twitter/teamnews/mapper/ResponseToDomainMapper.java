package com.sportal365.articlescheduler.sportsdata.twitter.teamnews.mapper;

import com.sportal365.articlescheduler.domain.model.MatchDetails;
import com.sportal365.articlescheduler.sportsdata.twitter.teamnews.model.TeamNewsData;

/**
 * Interface for mapping API response data to domain objects.
 * Separates the concern of domain object creation from content processing.
 */
public interface ResponseToDomainMapper {
    
    /**
     * Maps team news data to MatchDetails.TeamNews domain object.
     * 
     * @param teamNewsData The team news data from API response
     * @return MatchDetails.TeamNews object or null if mapping fails
     */
    MatchDetails.TeamNews mapToMatchDetailsTeamNews(TeamNewsData teamNewsData);
    
    /**
     * Maps team news data to MatchDetails.Quotes domain object.
     * 
     * @param teamNewsData The team news data from API response
     * @return MatchDetails.Quotes object or null if mapping fails
     */
    MatchDetails.Quotes mapToMatchDetailsQuotes(TeamNewsData teamNewsData);
    
    /**
     * Checks if the mapper can handle the given team news data.
     * 
     * @param teamNewsData The team news data to check
     * @return true if the mapper can process this data, false otherwise
     */
    boolean canMapData(TeamNewsData teamNewsData);
    
    /**
     * Gets the name of this mapper for logging and identification.
     * 
     * @return The mapper name
     */
    String getMapperName();
}
