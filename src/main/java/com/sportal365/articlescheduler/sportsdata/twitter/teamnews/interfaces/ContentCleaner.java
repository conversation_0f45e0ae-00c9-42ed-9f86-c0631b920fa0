package com.sportal365.articlescheduler.sportsdata.twitter.teamnews.interfaces;

/**
 * Focused interface for content cleaning operations.
 * Segregated from parsing and other processing concerns.
 */
public interface ContentCleaner {
    
    /**
     * Cleans content by removing unwanted elements.
     * 
     * @param content The content to clean
     * @return The cleaned content
     */
    String clean(String content);
    
    /**
     * Cleans content with specific cleaning options.
     * 
     * @param content The content to clean
     * @param options The cleaning options to apply
     * @return The cleaned content
     */
    String clean(String content, CleaningOptions options);
    
    /**
     * Validates that the content can be cleaned by this cleaner.
     * 
     * @param content The content to validate
     * @return true if the content can be cleaned
     */
    boolean canClean(String content);
    
    /**
     * Gets the cleaning capabilities of this cleaner.
     * 
     * @return The cleaning capabilities
     */
    CleaningCapabilities getCapabilities();
    
    /**
     * Options for content cleaning operations.
     */
    interface CleaningOptions {
        /**
         * Whether to remove formatting markers.
         * 
         * @return true if formatting should be removed
         */
        boolean removeFormatting();
        
        /**
         * Whether to normalize whitespace.
         * 
         * @return true if whitespace should be normalized
         */
        boolean normalizeWhitespace();
        
        /**
         * Whether to remove special symbols.
         * 
         * @return true if special symbols should be removed
         */
        boolean removeSpecialSymbols();
        
        /**
         * Whether to preserve line breaks.
         * 
         * @return true if line breaks should be preserved
         */
        boolean preserveLineBreaks();
    }
    
    /**
     * Capabilities of a content cleaner.
     */
    interface CleaningCapabilities {
        /**
         * Gets the types of content this cleaner can handle.
         * 
         * @return Array of supported content types
         */
        String[] getSupportedContentTypes();
        
        /**
         * Gets the cleaning operations supported.
         * 
         * @return Array of supported operations
         */
        String[] getSupportedOperations();
        
        /**
         * Whether this cleaner supports batch processing.
         * 
         * @return true if batch processing is supported
         */
        boolean supportsBatchProcessing();
    }
}
