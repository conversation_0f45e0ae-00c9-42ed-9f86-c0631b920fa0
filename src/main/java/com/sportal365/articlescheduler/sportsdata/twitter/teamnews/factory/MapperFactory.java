package com.sportal365.articlescheduler.sportsdata.twitter.teamnews.factory;

import com.sportal365.articlescheduler.sportsdata.twitter.teamnews.builder.PromptTemplateBuilder;
import com.sportal365.articlescheduler.sportsdata.twitter.teamnews.builder.XaiRequestBuilder;
import com.sportal365.articlescheduler.sportsdata.twitter.teamnews.mapper.ResponseToDomainMapper;

/**
 * Factory interface for creating different types of mappers and builders.
 * Implements the Factory pattern to abstract object creation.
 */
public interface MapperFactory {
    
    /**
     * Creates a request builder for the specified type.
     * 
     * @param builderType The type of builder to create
     * @return The created request builder
     */
    XaiRequestBuilder createRequestBuilder(String builderType);
    
    /**
     * Creates a response mapper for the specified type.
     * 
     * @param mapperType The type of mapper to create
     * @return The created response mapper
     */
    ResponseToDomainMapper createResponseMapper(String mapperType);
    
    /**
     * Creates a prompt template builder for the specified type.
     * 
     * @param templateType The type of template builder to create
     * @return The created prompt template builder
     */
    PromptTemplateBuilder createPromptTemplateBuilder(String templateType);
    
    /**
     * Gets the supported builder types.
     * 
     * @return Array of supported builder type names
     */
    String[] getSupportedBuilderTypes();
    
    /**
     * Gets the supported mapper types.
     * 
     * @return Array of supported mapper type names
     */
    String[] getSupportedMapperTypes();
    
    /**
     * Gets the supported template types.
     * 
     * @return Array of supported template type names
     */
    String[] getSupportedTemplateTypes();
    
    /**
     * Checks if the factory supports the given builder type.
     * 
     * @param builderType The builder type to check
     * @return true if supported, false otherwise
     */
    boolean supportsBuilderType(String builderType);
    
    /**
     * Checks if the factory supports the given mapper type.
     * 
     * @param mapperType The mapper type to check
     * @return true if supported, false otherwise
     */
    boolean supportsMapperType(String mapperType);
    
    /**
     * Checks if the factory supports the given template type.
     * 
     * @param templateType The template type to check
     * @return true if supported, false otherwise
     */
    boolean supportsTemplateType(String templateType);
}
