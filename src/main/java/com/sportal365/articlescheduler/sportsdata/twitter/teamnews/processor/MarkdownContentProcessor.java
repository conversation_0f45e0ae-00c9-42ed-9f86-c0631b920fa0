package com.sportal365.articlescheduler.sportsdata.twitter.teamnews.processor;

import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Component;

/**
 * Implementation of ContentParsingStrategy for Markdown content.
 * Handles parsing of Markdown-formatted team news content with ## and ### headers.
 */
@Slf4j
@Component
public class MarkdownContentProcessor implements ContentParsingStrategy {
    
    @Override
    public String extractSectionContent(String content, int sectionIndex) {
        if (content == null || content.isEmpty()) {
            return null;
        }

        // Split content by ## headers
        String[] sections = content.split("(?m)^## ");

        // The first element might be empty or contain content before the first ##
        // Adjust index to account for this
        int actualIndex = sectionIndex + 1; // +1 because split creates empty first element

        if (actualIndex >= sections.length) {
            log.debug("Section index {} not found in content with {} sections", sectionIndex, sections.length - 1);
            return null;
        }

        String sectionContent = sections[actualIndex];

        // Remove the header line (everything up to the first newline)
        int firstNewlineIndex = sectionContent.indexOf('\n');
        if (firstNewlineIndex != -1) {
            sectionContent = sectionContent.substring(firstNewlineIndex + 1);
        }

        return sectionContent.trim();
    }
    
    @Override
    public String[] parseTeamSections(String content) {
        if (content == null || content.isEmpty()) {
            return new String[0];
        }

        // Split by ### headers (team names)
        String[] sections = content.split("(?m)^### ");

        // Remove empty first section if it exists
        if (sections.length > 0 && sections[0].trim().isEmpty()) {
            String[] newSections = new String[sections.length - 1];
            System.arraycopy(sections, 1, newSections, 0, sections.length - 1);
            return newSections;
        }

        return sections.length > 1 ? java.util.Arrays.copyOfRange(sections, 1, sections.length) : sections;
    }
    
    @Override
    public boolean canHandle(String content) {
        if (content == null || content.isEmpty()) {
            return false;
        }
        
        // Check for Markdown headers (## or ###)
        return content.contains("##") || content.contains("###");
    }
}
