package com.sportal365.articlescheduler.sportsdata.twitter.teamnews.interfaces;

/**
 * Focused interface for content parsing operations.
 * Segregated from other content processing concerns.
 */
public interface ContentParser {
    
    /**
     * Parses content and extracts structured information.
     * 
     * @param content The content to parse
     * @return The parsed content structure
     */
    ParsedContent parse(String content);
    
    /**
     * Checks if this parser can handle the given content type.
     * 
     * @param contentType The content type to check
     * @return true if this parser supports the content type
     */
    boolean supports(String contentType);
    
    /**
     * Gets the content types supported by this parser.
     * 
     * @return Array of supported content types
     */
    String[] getSupportedContentTypes();
    
    /**
     * Data structure representing parsed content.
     */
    interface ParsedContent {
        /**
         * Gets the main content sections.
         * 
         * @return Array of content sections
         */
        String[] getSections();
        
        /**
         * Gets metadata about the parsed content.
         * 
         * @return Content metadata
         */
        ContentMetadata getMetadata();
        
        /**
         * Gets the original raw content.
         * 
         * @return The original content
         */
        String getRawContent();
    }
    
    /**
     * Metadata about parsed content.
     */
    interface ContentMetadata {
        /**
         * Gets the detected content type.
         * 
         * @return The content type
         */
        String getContentType();
        
        /**
         * Gets the number of sections found.
         * 
         * @return Section count
         */
        int getSectionCount();
        
        /**
         * Gets the parsing confidence score (0.0 to 1.0).
         * 
         * @return Confidence score
         */
        double getConfidence();
    }
}
