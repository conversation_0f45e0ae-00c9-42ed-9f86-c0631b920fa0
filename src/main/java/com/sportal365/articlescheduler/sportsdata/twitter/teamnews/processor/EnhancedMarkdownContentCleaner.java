package com.sportal365.articlescheduler.sportsdata.twitter.teamnews.processor;

import com.sportal365.articlescheduler.sportsdata.twitter.teamnews.chain.ContentProcessingPipeline;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Component;

import java.util.regex.Pattern;

/**
 * Enhanced implementation of ContentCleaningStrategy that uses the Chain of Responsibility pattern.
 * Combines the original cleaning logic with the new processing pipeline for better modularity.
 */
@Slf4j
@Component("enhancedMarkdownContentCleaner")
@RequiredArgsConstructor
public class EnhancedMarkdownContentCleaner implements ContentCleaningStrategy {
    
    private final ContentProcessingPipeline contentProcessingPipeline;
    
    // Pattern to match special symbols like **, ###, =, etc.
    private static final Pattern SPECIAL_SYMBOLS_PATTERN = Pattern.compile("[*#=\\-_`~]+");
    
    @Override
    public String cleanContent(String content) {
        if (content == null || content.isEmpty()) {
            return "";
        }

        log.debug("Starting basic content cleaning for content length: {}", content.length());

        // Remove special symbols
        String cleaned = SPECIAL_SYMBOLS_PATTERN.matcher(content).replaceAll("");

        // Clean up multiple spaces and newlines
        cleaned = cleaned.replaceAll("\\s+", " ");

        // Remove leading/trailing whitespace
        cleaned = cleaned.trim();

        // Remove team name from the beginning if it exists (first line)
        String[] lines = cleaned.split("\\n", 2);
        if (lines.length > 1) {
            cleaned = lines[1].trim();
        }

        log.debug("Basic content cleaning completed, result length: {}", cleaned.length());
        return cleaned;
    }
    
    @Override
    public String cleanAllContent(String content) {
        if (content == null || content.isEmpty()) {
            return "";
        }

        log.debug("Starting comprehensive content cleaning using processing pipeline");
        
        // Use the Chain of Responsibility pattern for comprehensive cleaning
        String result = contentProcessingPipeline.processContent(content);
        
        log.debug("Comprehensive content cleaning completed");
        return result;
    }
    
    @Override
    public String getStrategyName() {
        return "EnhancedMarkdownContentCleaner";
    }
}
