package com.sportal365.articlescheduler.sportsdata.twitter.teamnews.mapper;

import com.sportal365.articlescheduler.domain.model.MatchDetails;
import com.sportal365.articlescheduler.sportsdata.twitter.teamnews.model.TeamNewsData;
import com.sportal365.articlescheduler.sportsdata.twitter.teamnews.processor.JsonContentConverter;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Component;

@Slf4j
@Component
@RequiredArgsConstructor
public class TeamNewsResponseMapper {

    private final ResponseToDomainMapper responseToDomainMapper;
    private final JsonContentConverter jsonContentConverter;

    public MatchDetails.TeamNews mapToMatchDetailsTeamNews(TeamNewsData teamNewsData) {
        return responseToDomainMapper.mapToMatchDetailsTeamNews(teamNewsData);
    }

    public MatchDetails.Quotes mapToMatchDetailsQuotes(TeamNewsData teamNewsData) {
        return responseToDomainMapper.mapToMatchDetailsQuotes(teamNewsData);
    }

    /**
     * Converts MatchDetails.TeamNews content to clean JSON format.
     *
     * @param teamNews The TeamNews object containing markdown content
     * @return JSON string with clean content structure
     */
    public String convertTeamNewsToJson(MatchDetails.TeamNews teamNews) {
        return jsonContentConverter.convertTeamNewsToJson(teamNews);
    }

}