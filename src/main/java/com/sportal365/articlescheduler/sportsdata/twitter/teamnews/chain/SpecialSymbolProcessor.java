package com.sportal365.articlescheduler.sportsdata.twitter.teamnews.chain;

import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Component;

/**
 * Content processor that removes special symbols and formatting markers.
 * Part of the Chain of Responsibility pattern for content processing.
 */
@Slf4j
@Component
public class SpecialSymbolProcessor extends ContentProcessor {
    
    @Override
    protected String doProcess(String content) {
        if (content == null || content.isEmpty()) {
            return content;
        }
        
        String processed = content;
        int originalLength = content.length();
        
        // Remove bold/italic markers
        processed = processed.replaceAll("\\*{1,2}([^*]+)\\*{1,2}", "$1");
        
        // Remove bullet points and dashes
        processed = processed.replaceAll("(?m)^\\s*[-*+]\\s*", "");
        
        // Remove horizontal rules
        processed = processed.replaceAll("(?m)^\\s*[-=]{3,}\\s*$", "");
        
        // Remove underscores used for emphasis
        processed = processed.replaceAll("_{1,2}([^_]+)_{1,2}", "$1");
        
        // Remove backticks
        processed = processed.replaceAll("`([^`]+)`", "$1");
        
        int processedLength = processed.length();
        log.debug("Removed special symbols, content length: {} -> {}", originalLength, processedLength);
        
        return processed;
    }
    
    @Override
    protected boolean canProcess(String content) {
        return super.canProcess(content) && containsSpecialSymbols(content);
    }
    
    /**
     * Checks if the content contains special symbols that need processing.
     * 
     * @param content The content to check
     * @return true if special symbols are found
     */
    private boolean containsSpecialSymbols(String content) {
        if (content == null) {
            return false;
        }
        
        return content.contains("*") || 
               content.contains("_") || 
               content.contains("`") || 
               content.contains("-") || 
               content.contains("=");
    }
}
