import com.sportal365.articlescheduler.domain.model.MatchDetails;
import com.sportal365.articlescheduler.sportsdata.twitter.teamnews.mapper.TeamNewsResponseMapper;

public class TeamNewsJsonConversionExample {
    
    public static void main(String[] args) {
        // Your example content
        String exampleContent = "### Интер Маями\n" +
                "- **Състояние на отбора**: Интер Маями, воден от звездата Лионел Меси, започна участието си в Световното клубно първенство с мач срещу Ал-Ахли на 14 юни, както беше съобщено от sportal.bg. Отборът е в добра форма след силни изяви в MLS, а присъствието на Меси продължава да бъде основен фактор за морала и тактическата структура на тима.\n" +
                "- **Контузии и наказания**: Към момента няма конкретна информация за контузени или наказани играчи, но последните новини от Nostrabet.com (публикувано на 19.06.2025) подчертават, че ще бъдат предоставени актуални данни за състоянието на състава преди мача.\n" +
                "- **Вероятен състав**: Очаква се Меси да поведе атаката, подкрепен от играчи като Луис Суарес (ако е на разположение) и млади таланти от академията на клуба, които са получили шанс в последните мачове.\n\n" +
                "### Порто\n" +
                "- **Състояние на отбора**: Порто, един от представителите на Европа в турнира, пристига с богат опит от Шампионската лига на УЕФА и силни изяви в португалското първенство. Отборът е известен със своята дисциплинирана защита и бързи контраатаки.\n" +
                "- **Контузии и наказания**: Подобно на Интер Маями, няма конкретни данни за отсъстващи играчи към днешна дата, но Nostrabet.com съобщава, че ще има актуализации относно състоянието на отбора преди срещата.\n" +
                "- **Вероятен състав**: Очаква се Порто да разчита на ключови играчи като Пепе (ако е възстановен) в защита и опитни полузащитници, които да контролират темпото на играта.\n\n" +
                "---";

        // Create TeamNews object
        MatchDetails.TeamNews teamNews = MatchDetails.TeamNews.builder()
                .teamNews(exampleContent)
                .build();

        // Create mapper and convert to JSON
        TeamNewsResponseMapper mapper = new TeamNewsResponseMapper();
        String jsonResult = mapper.convertTeamNewsToJson(teamNews);
        
        System.out.println("Original content with special symbols:");
        System.out.println(exampleContent);
        System.out.println("\n" + "=".repeat(80) + "\n");
        System.out.println("Converted to clean JSON:");
        System.out.println(jsonResult);
    }
}
